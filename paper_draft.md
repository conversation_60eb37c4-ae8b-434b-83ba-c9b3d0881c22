# 面向室内 RFID 静态定位的异构图注意力模型

## 摘要（Abstract）

随着物联网技术的快速发展，基于 RFID 的室内定位技术在智能仓储、医疗监护和工业制造等应用中展现出巨大潜力。室内 RFID 定位技术在多径传播、信号衰减和环境噪声影响下面临精度受限的问题。针对传统方法难以有效整合空间拓扑信息与信号传播特性的局限，提出一种基于异构图注意力网络的 RFID 静态定位方法。该方法将标签与天线建模为异构图中的不同节点类型，构建标签-天线二元异构图结构。采用接收信号强度指示（RSSI）差分特征向量作为节点特征，通过自适应 K 近邻算法动态生成图拓扑，并基于距离衰减模型设计边权重。设计改进的 GATv2 异构图注意力网络，结合多头注意力机制和信号可靠性调节技术，实现异构节点间的自适应信息聚合。构建双阶段多模型融合框架，将多层感知机初步预测与异构图神经网络精确校正相结合。在室内环境数据集上的实验结果表明，与 LANDMARC、多层感知机、同构图注意力网络等基准方法相比，所提方法的平均定位误差降低 37.2%。该方法有效缓解了 RSSI 噪声与遮挡对定位精度的影响，为室内 RFID 定位系统提供了新的解决方案。

## 引言

随着物联网技术的快速发展，室内定位技术在智能仓储、医疗监护和工业制造等领域展现出巨大应用价值。射频识别（RFID）凭借其非视距识别、低功耗和高标签容量等特点，成为室内定位的重要技术手段[1]。现有 RFID 定位方法主要基于信号到达时间（TOA）、信号强度指示（RSSI）等测量原理[2]。其中，基于 RSSI 的方法因无需额外硬件改造且成本较低而广泛应用，但在复杂室内环境中面临多径传播、信号衰减和环境噪声等挑战，定位精度难以满足实际应用需求。

传统 RSSI 定位方法如 LANDMARC[4]通过信号强度相似性匹配实现定位，但难以有效处理信号波动和环境干扰。研究者们提出了多种改进算法，如基于密度聚类和神经网络的增强 LANDMARC[11]、采用自适应 K 近邻算法的改进方法[12]，以及基于数值插值和权重融合的优化算法[13]，显著提升了定位精度。然而，RSSI 信号本身存在多径衰落和环境敏感性问题，限制了进一步的精度提升。

为突破 RSSI 精度瓶颈，相位信息逐渐成为研究热点。相位测量能提供比 RSSI 更精细的位置感知能力，Huang 等人[14]提出了基于相位差分的精确定位方法，实现了厘米级定位精度；Zhang 等人[15]设计了结合相位和 RSSI 的移动平台定位系统，达到了亚米级精度；Liu 等人[16]提出基于深度学习的相位和 RSSI 融合方法 PRDL，显著提升了标签相对定位性能。然而，相位信息容易受到载波频率偏移和相位模糊的影响，在实际部署中面临稳定性挑战。

面对传统信号处理方法的局限性，机器学习技术为 RFID 定位带来了新的突破。Mo 等人[8]基于支持向量回归优化 RSSI 处理，Peng 等人[9]采用深度卷积网络融合多维信号特征，均取得了较好效果。这些方法通过自动特征提取和复杂模式识别显著改善了定位性能，但大多将定位问题建模为独立的回归任务，忽略了 RFID 系统中标签与天线间的空间拓扑关系和信号传播的物理规律。

图神经网络作为一种新兴的深度学习架构，能够有效建模节点间的复杂关系并捕获图结构中的空间依赖性[17]。图注意力网络（GAT）[3]通过自适应权重机制实现了更精准的节点特征聚合，在多个领域取得成功应用。然而，传统图神经网络多采用同构建模方式，难以处理 RFID 系统中标签和天线的异质性特征。异构图神经网络[10]通过区分不同类型节点和边关系，为处理此类问题提供了新的思路。

对此，本文提出一种基于异构图注意力网络的 RFID 室内定位方法。该方法将 RFID 定位环境建模为包含标签节点和天线节点的异构图，通过 RSSI 差分特征消除环境共模干扰，采用自适应 K 近邻算法构建动态图拓扑，并基于改进的 GATv2 架构实现异构节点间的信息聚合。同时，设计多模型融合框架，结合多层感知机的快速预测与图神经网络的结构化建模优势。实验结果表明，所提方法在室内环境中的平均定位误差相比基准方法降低了 37.2%，有效提升了 RFID 定位系统的精度和鲁棒性。

## 1. 系统的详细设计

### 1.1. 系统概述

如图 1 所示，本文提出了一种基于异构图注意力网络的多模型融合 RFID 室内定位系统。该系统由四个核心功能模块构成：信号预处理、异构图拓扑构建和多模型融合定位预测。系统以接收信号强度指示（RSSI）作为主要物理指纹，通过建模室内环境中标签与天线间的异质关系，实现高精度定位。

**信号预处理**：该模块负责采集 RFID 标签在多天线阵列下的 RSSI 测量值，并进行标准化处理以消除设备间的系统性偏差。通过构建天线间 RSSI 差分特征向量，有效缓解信号传播过程中的路径损耗和阴影衰落影响。差分特征的引入显著增强了系统对环境变化的鲁棒性，为后续的图结构建模提供稳定的输入基础。

**异构图拓扑构建**：该模块将室内定位问题抽象为异构图学习任务，构建包含标签节点和天线节点的二分图结构。标签节点承载待定位目标的信号特征，天线节点编码读写器的空间几何信息。系统采用自适应邻域发现机制动态确定图连接模式，在保证图连通性的同时控制计算复杂度。边属性通过空间距离特征刻画节点间的物理关系和信号传播特性。

**图注意力特征学习**：该模块基于图注意力网络架构实现异构节点间的信息聚合与特征学习。通过多层图卷积操作，系统能够捕获不同尺度的空间依赖关系。多头注意力机制使模型能够自适应地分配邻居节点的重要性权重，增强对关键空间线索的感知能力。跨类型注意力计算强化了标签-天线间的异质信息交互，残差连接机制有效缓解了深层图网络的过平滑问题。

**多模型融合定位预测**：该模块采用双阶段预测架构。初步定位阶段，MLP 基于 RSSI 差分特征预测目标坐标；精确校正阶段，异构图注意力网络利用初步预测作为先验信息，融合空间拓扑结构进行位置优化。系统通过线性组合集成两阶段结果，有效结合 MLP 快速预测与图神经网络结构化建模优势，提升定位精度。

通过上述四个模块的协同作用，系统能够充分利用 RFID 信号的多维特征和空间拓扑信息，在室内环境中实现高精度定位。

### 1.2. 数据预处理

数据预处理模块负责将原始 RFID 信号转换为适用于图神经网络的特征表示。本节介绍数据预处理的三个关键步骤：信号特征提取与标准化、RSSI 差分特征构建和非线性特征变换，旨在提高信号质量并为后续模型提供稳定的输入特征。

#### 1.2.1. 信号特征提取与标准化

本研究采用 UHF RFID 读写器获取标签的接收信号强度指示（RSSI）作为主要特征。对于环境中的每个标签 $t_i \in \mathcal{T}$，我们构建特征向量 $\mathbf{x}_i \in \mathbb{R}^{m}$，其中 $m$ 代表天线数量，每个标签记录各天线的 RSSI 值。考虑到 RFID 信号受多径效应和发射功率波动影响，我们对原始 RSSI 值应用 MinMax 标准化：

$$
\text{RSSI}_{\text{norm},i}^{(a)} = \frac{\text{RSSI}_i^{(a)} - \min(\mathcal{R}^{(a)})}{\max(\mathcal{R}^{(a)}) - \min(\mathcal{R}^{(a)}) + \epsilon}
$$

其中，$\text{RSSI}_i^{(a)}$ 表示第 $i$ 个标签在天线 $a$ 接收的原始信号强度，$\mathcal{R}^{(a)}$ 代表天线 $a$ 接收的所有 RSSI 值集合，$\epsilon$ 为小正数防止分母为零（通常取 $\epsilon=10^{-6}$）。

#### 1.2.2. RSSI 差分特征构建

本研究使用基于差分信号处理的 RSSI 特征提取方法[5]，差分运算可以消除环境共模干扰（如发射功率波动或温度变化）在差分过程中被自动抵消，增强了特征的环境不变性和定位系统在复杂环境中的鲁棒性。对于部署的 m 个天线，我们构造差分特征矩阵，其中对任意两个天线 $a_i$ 和 $a_j$ 的差分特征定义为：

$$
\Delta\text{RSSI}_{i,j}^{(t)} = \text{RSSI}_{\text{norm}}^{(t,a_i)} - \text{RSSI}_{\text{norm}}^{(t,a_j)}, \quad \forall i,j \in \{1,2,...,m\}, i < j
$$

通过计算任意两天线间 RSSI 差值，我们获得了$C_m^2=\frac{m(m-1)}{2}$维的差分特征向量，为后续的图构建提供更为稳健的输入特征。

#### 1.2.3. 非线性特征变换

RSSI 信号与物理位置间的关系高度非线性，仅依靠线性特征难以捕获其内在映射关系。为此，我们采用简单有效的非线性变换增强特征表达能力。对于每个标签的 RSSI 原始特征和差分特征，我们应用如下非线性映射：

$$
f(\mathbf{x}) = \sigma(\mathbf{W} \cdot \mathbf{x} + \mathbf{b})
$$

这里，$f(\mathbf{x})$是非线性变换后的特征向量，$\mathbf{x} = [\mathbf{x}_{\text{raw}}; \mathbf{x}_{\text{diff}}]$ 为级联特征向量，$\mathbf{W}$是可学习的权重矩阵，$\mathbf{b}$是偏置项，$\sigma$为 ReLU 激活函数。此非线性映射能有效处理 RSSI 信号在不同方位的非均匀衰减特性，形成更具判别力的特征表示。

### 1.3. 异构图构建

传统机器学习方法难以充分利用 RFID 系统中标签与天线间的空间拓扑信息。异构图作为一种结构化数据表示方法，能够显式建模不同类型实体间的复杂关系。本节基于图论原理，将 RFID 定位问题转化为异构图学习任务，通过数学化建模捕获系统的空间几何特性。

#### 1.3.1 异构图模型定义

本研究提出的异构图模型是对 RFID 定位环境的结构化表示，形式化定义为 $\mathcal{G} = (\mathcal{V}, \mathcal{E}, \mathcal{A}, \mathcal{R})$，其中 $\mathcal{V}$ 表示节点集合，$\mathcal{E}$ 表示边集合，$\mathcal{A}$ 表示节点类型映射函数，$\mathcal{R}$ 表示边类型映射函数。与传统同构图不同，我们的异构图包含两种类型的节点：标签节点集 $\mathcal{V}_T$ 和天线节点集 $\mathcal{V}_A$，即 $\mathcal{V} = \mathcal{V}_T \cup \mathcal{V}_A$。每种节点类型具有独特的特征维度和语义信息，通过 $\phi: \mathcal{V} \rightarrow \mathbb{R}^{d_v}$ 映射到相应的特征空间，其中 $d_v$ 为节点类型相关的特征维度。

边集合 $\mathcal{E}$ 由四种关系类型组成：标签-标签（T-T）、标签-天线（T-A）、天线-标签（A-T）和天线-天线（A-A）关系。每种边类型 $r \in \mathcal{R}$ 具有特定的语义含义和边属性函数 $\psi_r: \mathcal{E}_r \rightarrow \mathbb{R}^{d_e}$，将边映射到 $d_e$ 维特征空间。

#### 1.3.2 动态拓扑生成

异构图的拓扑结构直接影响模型的特征学习能力和定位精度。本研究提出基于自适应邻域发现的动态拓扑生成算法，根据节点的局部分布特征和空间关系构建最优图连接模式。

为平衡图结构的稀疏性与连通性，采用基于局部密度的自适应策略动态调整邻居数量。对于标签节点 $i$ 和 $j$，首先计算其在多天线 RSSI 空间中的欧氏距离：

$$d_{ij} = \sqrt{\sum_{m=1}^{M} (r_{im} - r_{jm})^2}$$

基于距离度量计算标签 $i$ 的局部密度：

$$\rho_i = \sum_j \exp\left(-\frac{d_{ij}^2}{2\sigma^2}\right)$$

通过局部密度动态确定邻居数量：

$$K_i = \max(K_{min}, \min(K_{max}, K_{base} + \lfloor \beta \cdot \log(1 + \rho_i) \rfloor))$$

其中 $\sigma$ 为带宽参数，$K_{base}$ 为基础邻居数量，$\beta$ 为密度敏感系数，$K_{min}$ 和 $K_{max}$ 为邻居数量边界。该机制使密集区域标签采用较少邻居避免噪声干扰，稀疏区域标签增加邻居获得充分空间约束。

针对不同节点类型采用差异化连接策略：标签-标签边基于上述自适应算法连接 K 个最近邻标签；标签-天线边采用全连接模式捕获完整信号传播信息；天线-标签边基于信号覆盖范围建立连接；天线-天线边基于空间距离阈值构建协作网络。该策略确保图结构既能捕获局部空间相关性，又能保持全局连通性。

#### 1.3.3 边权重建模

异构图中的边权重量化不同类型节点间的相互作用强度。本研究采用基于距离衰减的边权重建模方法，通过数学函数描述节点间的空间关系和信号传播特性。

对于标签节点间的连接边 $(v_i, v_j) \in \mathcal{E}_{T-T}$，边权重基于 RSSI 特征空间中的相似性度量：

$$w_{ij}^{T-T} = \frac{1}{1 + d_{rssi}^2(v_i, v_j)}$$

其中 $d_{rssi}(v_i, v_j) = \sqrt{\sum_{m=1}^{M} (r_{im} - r_{jm})^2}$ 为标签在多天线 RSSI 空间中的欧氏距离。该权重函数采用平方反比衰减形式，使 RSSI 特征相似的标签节点具有更强连接权重。

对于标签-天线边 $(v_i, v_j) \in \mathcal{E}_{T-A}$，边权重基于空间距离的物理衰减模型：

$$w_{ij}^{T-A} = \frac{1}{1 + d_{spatial}^2(v_i, v_j)}$$

该权重函数遵循自由空间路径损耗规律，反映 RFID 信号强度随距离衰减的物理特性。对于天线-天线边 $(v_i, v_j) \in \mathcal{E}_{A-A}$，采用相同的空间距离衰减模型，反映天线间的空间相关性，使距离较近的天线具有更强协作关系。

### 1.4. 图注意力特征学习

异构图注意力网络通过自适应权重分配机制实现不同类型节点间的信息聚合。本研究采用三层 GATv2[7] 架构，结合多头注意力机制和信号可靠性调节技术，对 RFID 定位环境中的复杂空间关系进行建模。

#### 1.4.1 GATv2 注意力机制

本研究采用 GATv2 改进架构替代传统 GAT[6]，通过将注意力计算移至线性变换之后，提升模型表达能力。对于异构图 $\mathcal{G} = (\mathcal{V}, \mathcal{E}, \mathcal{A}, \mathcal{R})$ 中的节点 $v_i \in \mathcal{V}$，注意力权重计算公式为：

$$\alpha_{ij}^{(r)} = \frac{\exp(\text{LeakyReLU}(\mathbf{a}_r^T \cdot \mathbf{W}_r [\mathbf{h}_i \| \mathbf{h}_j \| \mathbf{e}_{ij}]))}{\sum_{k \in \mathcal{N}_i^{(r)}} \exp(\text{LeakyReLU}(\mathbf{a}_r^T \cdot \mathbf{W}_r [\mathbf{h}_i \| \mathbf{h}_k \| \mathbf{e}_{ik}]))}$$

其中，$\mathbf{h}_i$ 和 $\mathbf{h}_j$ 分别表示节点 $i$ 和 $j$ 的特征向量，$\mathbf{e}_{ij}$ 为边特征，$\mathbf{W}_r \in \mathbb{R}^{d_{out} \times (2d_{in} + d_e)}$ 为关系类型 $r$ 的权重矩阵，$\mathbf{a}_r \in \mathbb{R}^{d_{out}}$ 为注意力参数向量，$\mathcal{N}_i^{(r)}$ 表示节点 $i$ 在关系 $r$ 下的邻居集合，$\|$ 表示特征拼接操作。

节点特征更新公式为：

$$\mathbf{h}_i^{(l+1)} = \sigma\left(\sum_{r \in \mathcal{R}} \sum_{j \in \mathcal{N}_i^{(r)}} \alpha_{ij}^{(r)} \mathbf{W}_r^{(l)} \mathbf{h}_j^{(l)}\right)$$

其中，$\sigma$ 为 ELU 激活函数，$l$ 表示网络层数。该机制根据不同关系类型自适应分配注意力权重，有效捕获异构节点间的交互模式。算法复杂度为 $O(|E| \cdot d_{hidden} \cdot H)$，其中 $|E|$ 为边数，$H$ 为注意力头数。

#### 1.4.2 多头注意力聚合

采用多头注意力机制将注意力计算并行化为 $H$ 个独立的注意力头，增强模型表达能力。对于第 $h$ 个注意力头，其输出特征为：

$$\mathbf{h}_i^{(l+1,h)} = \sum_{r \in \mathcal{R}} \sum_{j \in \mathcal{N}_i^{(r)}} \alpha_{ij}^{(r,h)} \mathbf{W}_r^{(l,h)} \mathbf{h}_j^{(l)}$$

采用拼接聚合方式保持不同注意力头的独立性：

$$\mathbf{h}_i^{(l+1)} = \text{Concat}(\mathbf{h}_i^{(l+1,1)}, \mathbf{h}_i^{(l+1,2)}, \ldots, \mathbf{h}_i^{(l+1,H)})$$

本文设计了分层递减的多头注意力架构，在网络浅层部署多个注意力头以并行学习天线-标签间的多维度关系特征，随着网络层次加深逐步减少注意力头数量，最终收敛至单一输出层。针对深层图神经网络在特征聚合过程中易出现的梯度消失与特征退化现象，本文引入残差连接机制[6]。该机制通过建立跨层直连通道，使关键信号特征能够绕过中间层直接传递至深层网络，有效缓解了信号特征在多层传播中的衰减问题，从而提升定位精度并增强系统的稳定性与鲁棒性。

#### 1.4.3 信号可靠性调节

针对 RFID 信号的不稳定性和多径效应，设计基于信号可靠性的自适应特征调节机制。该机制根据标签节点的信号特征动态评估其可靠性并调整特征权重。

对于标签节点 $t_i$，其信号可靠性评分通过两层神经网络计算：

$$\rho_i = \sigma(\mathbf{W}_{\rho_2} \cdot \text{ReLU}(\mathbf{W}_{\rho_1} \mathbf{h}_{t_i} + \mathbf{b}_{\rho_1}) + \mathbf{b}_{\rho_2})$$

其中，$\mathbf{W}_{\rho_1} \in \mathbb{R}^{16 \times d_{hidden}}$ 和 $\mathbf{W}_{\rho_2} \in \mathbb{R}^{1 \times 16}$ 为可靠性评估网络的权重矩阵，$\sigma$ 为 Sigmoid 激活函数。

采用分层自适应缩放策略，在不同网络层应用不同的调节强度：

对于第 $l$ 层图卷积，其输出调整机制可统一表示为：
$$\mathbf{h}_{t_i}^{(l,adjusted)} = \mathbf{h}_{t_i}^{(l)} \cdot (\alpha_l + (1-\alpha_l) \cdot \rho_i)$$

其中，$\alpha_l$ 为第 $l$ 层网络的基础权重系数，控制各层信号可靠性调节强度。本文采用分层递进策略：浅层保持较高权重以保留原始信号特征，深层逐步降低权重以增强可靠性调节效果，实现信号完整性与特征鲁棒性的平衡。
通过 GATv2 注意力机制、多头注意力聚合和信号可靠性调节等技术，模型充分利用异构图结构中的空间拓扑信息和信号传播特性，实现对 RFID 定位环境的精确建模。

### 1.5. 多模型融合定位预测

基于前述异构图注意力网络和信号特征学习机制，本章提出多模型融合定位预测框架，集成多层感知机的快速预测能力与异构图神经网络的结构化建模优势，实现高精度室内定位。

该框架采用双阶段协同预测机制：第一阶段，多层感知机基于 RSSI 差分特征向量进行初步位置估计；第二阶段，异构图神经网络利用初步预测结果作为空间先验，通过先验融合网络将 MLP 预测信息嵌入到图神经网络的特征表示中，结合拓扑关系进行精确校正。双阶段预测的数学表达为：

$$\hat{\mathbf{p}}_{mlp} = f_{MLP}(\mathbf{x}_{diff})$$

$$\mathbf{h}_{enhanced} = f_{fusion}(\mathbf{h}_{tag}, \hat{\mathbf{p}}_{mlp})$$

$$\hat{\mathbf{p}}_{hetero} = f_{GAT}(\mathcal{G}, \mathbf{H}_{enhanced})$$

其中，$\mathbf{x}_{diff}$ 为 RSSI 差分特征向量，$f_{MLP}$ 为多层感知机映射函数，$f_{fusion}$ 为先验融合网络，$\mathbf{h}_{tag}$ 为标签节点特征，$\mathbf{H}_{enhanced}$ 为融合 MLP 先验后的增强节点特征矩阵，$f_{GAT}$ 为异构图注意力网络映射函数，$\mathcal{G}$ 为异构图结构。

最终融合预测通过加权线性组合实现：

$$\hat{\mathbf{p}}_{fusion} = w_{hetero} \cdot \hat{\mathbf{p}}_{hetero} + (1 - w_{hetero}) \cdot \hat{\mathbf{p}}_{mlp}$$

其中，$w_{hetero}$ 为异构图模型的融合权重。系统采用固定权重分配策略，通过权重参数控制不同模型的贡献度，确保预测过程的稳定性和可重现性。该融合框架有效整合了两种模型的优势，为复杂室内环境中的 RFID 定位提供精确鲁棒的解决方案。

## 参考文献（References）

1. Zafari F.; Gkelias A.; Leung K. K. _A Survey of Indoor Localization Systems and Technologies_. IEEE Communications Surveys & Tutorials, 2019, 21(3): 2568-2599.
2. Dardari D.; Closas P.; Djurić P. M. _Indoor Tracking: Theory, Methods, and Technologies_. IEEE Transactions on Vehicular Technology, 2015, 64(4): 1263-1278.
3. Veličković P.; Cucurull G.; Casanova A.; Romero A.; Liò P.; Bengio Y. _Graph Attention Networks_. International Conference on Learning Representations (ICLR), 2018.
4. Ni L. M.; Liu Y.; Lau Y. C.; Patil A. P. _LANDMARC: Indoor Location Sensing Using Active RFID_. Wireless Networks, 2004, 10(6): 701-710.
5. Wang J.; Gao Q.; Wang H.; Chen H.; Jin M. _Differential Radio Map-Based Robust Indoor Localization_. EURASIP Journal on Wireless Communications and Networking, 2011, 2011: 17.
6. Veličković P.; Cucurull G.; Casanova A.; Romero A.; Liò P.; Bengio Y. _Graph Attention Networks_. arXiv preprint arXiv:1710.10903, 2017.
7. Brody S.; Alon U.; Yahav E. _How Attentive are Graph Attention Networks?_ International Conference on Learning Representations (ICLR), 2022.
8. Mo L.; Zhu Y.; Zhang D. _UHF RFID Indoor Localization Algorithm Based on BP-SVR_. IEEE Journal of Radio Frequency Identification, 2022, 6: 385-393.
9. Peng C.; Jiang H.; Qu L. _Deep Convolutional Neural Network for Passive RFID Tag Localization via Joint RSSI and PDOA Fingerprint Features_. IEEE Access, 2021, 9: 15441-15451.
10. Wang X.; Ji H.; Shi C.; Wang B.; Cui P.; Yu P. S.; Ye Y. _Heterogeneous Graph Attention Network_. The World Wide Web Conference (WWW), 2019: 2022-2032.
11. Ren J.; Bao K.; Zhang G.; Chu L.; Lu W. _LANDMARC Indoor Positioning Algorithm Based on Density-based Spatial Clustering of Applications with Noise–Genetic Algorithm–Radial Basis Function Neural Network_. International Journal of Distributed Sensor Networks, 2020, 16(3): 1550147720907831.
12. Zhao Y.; Liu Y.; Ni L. M. _Advanced LANDMARC with Adaptive k-nearest Algorithm for RFID Location System_. Proceedings of the 1st International Conference on Scalable Information Systems, 2006: 42-48.
13. Li Y.; Chen Q.; Wang H. _Research on Improved RFID Indoor Location Algorithm Based on LANDMARC_. Proceedings of the 2022 4th International Conference on Robotics, Intelligent Control and Artificial Intelligence, 2022: 912-917.
14. Huang C.; Lee R.; Chen H. _Accurate Localization of RFID Tags Using Phase Difference_. IEEE International Conference on RFID, 2010: 276-283.
15. Zhang X.; Wang Y.; Li M. _A Phase and RSSI-Based Method for Indoor Localization Using Passive RFID System With Mobile Platform_. IEEE Access, 2022, 10: 34925-34935.
16. Liu Z.; Yang L.; Wang Q.; Li X. _PRDL: Relative Localization Method of RFID Tags via Phase and RSSI Based on Deep Learning_. IEEE Access, 2019, 7: 20249-20261.
17. Wu Z.; Pan S.; Chen F.; Long G.; Zhang C.; Yu P. S. _A Comprehensive Survey on Graph Neural Networks_. IEEE Transactions on Neural Networks and Learning Systems, 2021, 32(1): 4-24.
